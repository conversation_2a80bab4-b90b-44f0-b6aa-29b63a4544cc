import 'echarts-gl'
import * as echarts from 'echarts'
import 虫情种类 from '../jaxrs/constants/com.zny.ia.constant.DeviceConstant$虫情种类.js'
import 农作物种类 from '../jaxrs/constants/com.zny.ia.constant.ProdConstant$农作物种类.js'
import HomePageService from '../jaxrs/concrete/com.zny.ia.api.HomePageService.js'
import RemoteSensingService from '../jaxrs/concrete/com.zny.ia.api.RemoteSensingService.js'
import IrrigationService from '../jaxrs/concrete/com.zny.ia.api.IrrigationService'
let LINE_NUM_EACH_ROW = 6
let chart = {
    // 农作物溯源信息柱状图信息
    traceabilityInfoBar() {
        return HomePageService.homeCropSource().then(res => {
            if (res.length == 0 || res == null) {
                return {
                    show: false,
                    option: {},
                }
            }
            let name = [],
                data = []
            res.forEach(v => {
                name.push(v.yearMonth)
                data.push(v.count)
            })
            return {
                show: true,
                option: {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: 'cross', // 默认为直线，可选为：'line' | 'shadow'
                            lineStyle: {
                                color: '#FFCD35',
                            },
                            label: {
                                show: false,
                            },
                        },
                        showContent: false,
                        extraCssText: 'z-index:2',
                    },
                    grid: {
                        left: '5%',
                        right: '5%',
                        bottom: '5%',
                        top: '18%',
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: name,
                            axisTick: {
                                show: false,
                                alignWithLabel: true,
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: '#669DFF',
                                },
                            },
                            axisLabel: {
                                color: '#DFEEF3',
                                // interval: 0,
                                // rotate: 45
                            },
                        },
                    ],
                    yAxis: [
                        {
                            name: '单位/次',
                            nameTextStyle: {
                                color: '#DFEEF3',
                            },
                            axisLabel: {
                                color: '#DFEEF3',
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: '#669DFF',
                                },
                            },
                            splitLine: {
                                lineStyle: {
                                    type: 'dashed',
                                    // color:'#669DFF'
                                    color: 'rgba(25,225,203,0.2)',
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: '',
                            type: 'bar',
                            barWidth: '14',
                            data: data,
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                                        { offset: 0, color: 'rgba(25,225,203, 0)' },
                                        { offset: 1, color: '#19E1CB' },
                                    ]),
                                    barBorderRadius: [10, 10, 0, 0],
                                },
                            },
                        },
                    ],
                    label: {
                        normal: {
                            show: true,
                            position: 'top',
                            formatter: '{c}',
                            color: '#fff',
                        },
                    },
                    emphasis: {
                        label: {
                            show: true,
                            position: [-10, -20],
                            color: '#FFCD35',
                        },
                        itemStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [
                                    {
                                        offset: 0,
                                        color: '#F6A334', // 0% 处的颜色
                                    },
                                    {
                                        offset: 1,
                                        color: 'rgba(246,163,52,0)', // 100% 处的颜色
                                        // color:'#F390AA'
                                    },
                                ],
                                global: false, // 缺省为 false
                            },
                        },
                    },
                },
            }
        })
        // return Promise.resolve().then(() => {

        // })
    },
    // 电商交易占比饼图数据
    transactionProportionPie() {
        return HomePageService.homeMarketProportion().then(res => {
            if (res.length == 0 || res == null) {
                return {
                    show: false,
                    option: {},
                }
            }
            let data = []
            res.forEach(v => {
                let obj = {
                    value: v.proportion,
                    name: 农作物种类._lableOf(v.cropName),
                }
                data.push(obj)
            })
            let color = ['#38B6FE', '#C35BF3', '#2BF7E0', '#FFBF51']
            return {
                show: true,
                option: {
                    tooltip: {
                        trigger: 'item',
                        extraCssText: 'z-index:2',
                        backgroundColor: 'rgba(0,0,0,.5)',
                        borderColor: 'transparent',
                        textStyle: {
                            color: '#DFEEF3',
                        },
                    },
                    grid: {
                        left: '5%',
                        right: '15%',
                        bottom: '15%',
                        top: '20%',
                        containLabel: true,
                    },
                    legend: {
                        orient: 'vertical',
                        right: 20,
                        bottom: 20,
                        itemWidth: 15,
                        itemHeight: 15,
                        textStyle: {
                            color: '#fff',
                            fontSize: 14,
                        },
                    },
                    series: [
                        {
                            type: 'pie',
                            radius: '60%',
                            data: data.map((item, index) => {
                                item.label = {
                                    color: color[index],
                                }
                                return item
                            }),
                            itemStyle: {
                                color: params => {
                                    var index = params.dataIndex
                                    return color[index]
                                },
                            },
                            labelLine: {
                                show: true, //数据标签引导线
                                length: 50,
                                lineStyle: {
                                    width: 1,
                                    type: 'solid',
                                },
                            },
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)',
                                },
                            },
                        },
                    ],
                },
            }
        })
    },
    // 近七天报警数折线图数据
    alarmSevenDaysLine(list) {
        let name = [],
            data = []
        list.forEach(v => {
            name.push(v.day)
            data.push(v.count)
        })
        return Promise.resolve().then(() => {
            return {
                show: true,
                option: {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            lineStyle: {
                                color: 'transparent',
                                type: 'dashed',
                            },
                        },
                        extraCssText: 'z-index:2',
                        backgroundColor: 'rgba(0,0,0,.5)',
                        borderColor: 'transparent',
                        textStyle: {
                            color: '#DFEEF3',
                        },
                        formatter: function(params) {
                            return `<div style="text-align: center; color: #DFEEF3; font-size: 14px;">${params[0].name}: <span style="color: #E9A31F; font-weight: bold;">${params[0].value}次</span></div>`;
                        }
                    },
                    grid: {
                        left: '10%',
                        right: '8%',
                        top: '5%',
                        bottom: '18%',
                        containLabel: false,
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        // data: ['1.11', '1.12', '1.13', '1.14', '1.15', '1.16', '1.17'],
                        data: name,
                        axisTick: {
                            show: false,
                            alignWithLabel: true,
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#28DFC4',
                            },
                        },
                        axisLabel: {
                            color: '#DFEEF3',
                        },
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            show: false,
                            color: '#B2CFFF',
                        },
                        axisTick: {
                            show: false,
                        },
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: '#669EFF',
                            },
                        },
                        splitLine: {
                            show: false,
                            lineStyle: {
                                color: 'rgba(102,158,255,0.2)',
                                type: 'dashed',
                            },
                        },
                    },
                    series: [
                        {
                            // data: [93, 932, 901, 15, 820, 1330, 150],
                            data: data,
                            type: 'line',
                            symbol: 'circle',
                            symbolSize: 8,
                            showSymbol: false, // 默认不显示标记点
                            // 鼠标经过时显示标记点
                            emphasis: {
                                focus: 'series',
                                itemStyle: {
                                    borderWidth: 2,
                                    borderColor: '#28DFC4',
                                    color: '#28DFC4'
                                }
                            },

                            // smooth: true,
                            areaStyle: {
                                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                                    { offset: 1, color: 'rgba(40,223,196, 0.7)' },
                                    { offset: 0, color: 'rgba(40,223,196, 0.02)' },
                                ]),
                            },
                            itemStyle: {
                                color: '#28DFC4',
                            },
                        },
                    ],
                },
            }
        })
    },
    // 气象——>温湿度折线图数据
    THLineToday(list) {
        let name = [],
            Tdata = [],
            Hdata = []
        
        if (list && list.length > 0) {
            list.forEach(v => {
                name.push(v.showTime)
                Tdata.push(v.temperature)
                Hdata.push(v.humidity)
            })
        }
        
        return Promise.resolve().then(() => {
            // 如果没有数据，返回不显示图表
            if (name.length === 0) {
                return {
                    show: false,
                    option: {}
                }
            }
            
            return {
                show: true,
                option: {
                    legend: {
                        top: '8%',
                        left: 'center',
                        textStyle: {
                            color: '#DFEEF3',
                            fontSize: 14,
                        },
                        data: [
                            {
                                name: '温度',
                                icon: 'rect',
                                itemStyle: {
                                    color: '#52F9BC'
                                }
                            },
                            {
                                name: '湿度',
                                icon: 'rect',
                                itemStyle: {
                                    color: '#34A7FF'
                                }
                            }
                        ],
                        itemWidth: 14,
                        itemHeight: 2,
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross',
                            lineStyle: {
                                color: '#FFFFFF',
                                type: 'dashed',
                            },
                            label: {
                                show: false
                            }
                        },
                        extraCssText: 'z-index:2',
                        backgroundColor: 'rgba(0,0,0,.5)',
                        borderColor: 'transparent',
                        textStyle: {
                            color: '#DFEEF3',
                            fontSize: 14,
                        },
                        formatter: function(params) {
                            let result = '<div style="text-align: center;">'
                            result += '<div style="font-size: 14px; color: #DFEEF3; margin-bottom: 4px;">' + params[0].name + '</div>'
                            result += '<div style="display: inline-block; text-align: left;">'
                            params.forEach(function(item) {
                                let unit = item.seriesName === '温度' ? '℃' : '%RH'
                                let color = item.seriesName === '温度' ? '#52F9BC' : '#34A7FF'
                                result += '<div style="font-size: 14px; color: #DFEEF3; white-space: nowrap;">' + item.seriesName + ': ' +
                                         '<span style="color:' + color + '">' + item.value + unit + '</span></div>'
                            })
                            result += '</div></div>'
                            return result
                        }
                    },
                    grid: {
                        left: '2%',
                        right: '4%',
                        top: '25%',
                        bottom: '15%',
                        containLabel: true,
                    },
                    xAxis: {
                        type: 'category',
                        data: name,
                        boundaryGap: false,
                        axisTick: {
                            show: false,
                            alignWithLabel: true,
                        },
                        axisLine: {
                            lineStyle: {
                                color: 'rgba(0,242,255,0.2)',
                                type: 'solid',
                            },
                        },
                        axisLabel: {
                            color: '#DFEEF3',
                            fontSize: 12,
                            // 智能调整x轴标签显示间隔，避免拥挤
                            interval: name && name.length > 15 ? Math.ceil(name.length / 8) - 1 : 'auto',
                            rotate: name && name.length > 20 ? 45 : 0,
                        },
                    },
                    yAxis: [
                        {
                            type: 'value',
                            name: '温度(℃)',
                            nameLocation: 'end',
                            nameGap: 25,
                            nameTextStyle: {
                                color: '#DFEEF3',
                                fontSize: 14,
                                padding: [0, 0, 0, 40], // [上, 右, 下, 左] 调整左边距来改变横向位置
                            },
                            position: 'left',
                            axisLabel: {
                                color: '#DFEEF3',
                                fontSize: 12,
                                formatter: function(value) {
                                    return Math.round(value);
                                }
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: 'rgba(255,255,255,0.2)',
                                    type: 'solid',
                                },
                            },
                            splitLine: {
                                lineStyle: {
                                    color: 'rgba(255,255,255,0.2)',
                                    type: 'dashed',
                                },
                            },
                        },
                        {
                            type: 'value',
                            name: '湿度(%RH)',
                            nameLocation: 'end',
                            nameGap: 25,
                            nameTextStyle: {
                                color: '#DFEEF3',
                                fontSize: 14,
                                padding: [0, 0, 0, 40], // [上, 右, 下, 左] 调整左边距来改变横向位置
                            },
                            position: 'right',
                            // 动态自适应y轴范围
                            min: function(value) {
                                return Math.max(0, value.min - (value.max - value.min) * 0.1);
                            },
                            max: function(value) {
                                return Math.min(100, value.max + (value.max - value.min) * 0.1);
                            },
                            axisLabel: {
                                color: '#DFEEF3',
                                fontSize: 12,
                                formatter: function(value) {
                                    return Math.round(value) + '%';
                                }
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: 'rgba(255,255,255,0.2)',
                                    type: 'solid',
                                },
                            },
                            splitLine: {
                                show: false,
                            },
                        },
                    ],
                    series: [
                        {
                            data: Tdata,
                            type: 'line',
                            yAxisIndex: 0,
                            name: '温度',
                            symbol: 'none', // 去除折线上的圆点
                            itemStyle: {
                                normal: {
                                    color: '#52F9BC',
                                },
                            },
                            lineStyle: {
                                width: 2,
                                color: '#52F9BC',
                            }
                        },
                        {
                            data: Hdata,
                            type: 'line',
                            yAxisIndex: 1,
                            name: '湿度',
                            symbol: 'none', // 去除折线上的圆点
                            itemStyle: {
                                normal: {
                                    color: '#34A7FF',
                                },
                            },
                            lineStyle: {
                                width: 2,
                                color: '#34A7FF',
                            }
                        },
                    ],
                },
            }
        })
    },
    THLine(list) {
        let name = [],
            TMax = [],
            TMin = [],
            TAvg = [],
            HAvg = []
        
        if (list && list.length > 0) {
            list.forEach(v => {
                name.push(v.showTime)
                TMax.push(v.temperatureMax)
                TMin.push(v.temperatureMin)
                TAvg.push(v.temperatureAvg)
                HAvg.push(v.humidityAvg)
            })
        }
        
        return Promise.resolve().then(() => {
            // 如果没有数据，返回不显示图表
            if (name.length === 0) {
                return {
                    show: false,
                    option: {}
                }
            }
            
            return {
                show: true,
                option: {
                    legend: {
                        top: '8%',
                        left: 'center',
                        textStyle: {
                            color: '#DFEEF3',
                            fontSize: 14,
                        },
                        data: [
                            {
                                name: '温度',
                                icon: 'rect',
                                itemStyle: {
                                    color: '#52F9BC'
                                }
                            },
                            {
                                name: '湿度',
                                icon: 'rect',
                                itemStyle: {
                                    color: '#34A7FF'
                                }
                            }
                        ],
                        itemWidth: 14,
                        itemHeight: 2,
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross',
                            lineStyle: {
                                color: '#FFFFFF',
                                type: 'dashed',
                            },
                            label: {
                                show: false
                            }
                        },
                        extraCssText: 'z-index:2',
                        backgroundColor: 'rgba(0,0,0,.5)',
                        borderColor: 'transparent',
                        textStyle: {
                            color: '#DFEEF3',
                            fontSize: 14,
                        },
                        formatter: function(params) {
                            let result = '<div style="text-align: center; font-size: 14px;">' + params[0].name + '</div>'
                            params.forEach(function(item) {
                                let unit = item.seriesName.includes('温度') ? '℃' : '%RH'
                                result += '<div style="text-align: center; font-size: 14px; color:' + item.color + '">' +
                                         item.seriesName + ': ' + item.value + unit + '</div>'
                            })
                            return result
                        }
                    },
                    grid: {
                        left: '2%',
                        right: '4%',
                        top: '20%',
                        bottom: '15%',
                        containLabel: true,
                    },
                    xAxis: {
                        type: 'category',
                        // data: ['5-12', '5-13', '5-14', '5-15', '5-16', '5-17', '5-18'],
                        data: name,
                        boundaryGap: false,
                        axisTick: {
                            show: false,
                            alignWithLabel: true,
                        },
                        axisLine: {
                            lineStyle: {
                                color: 'rgba(0,242,255,0.2)',
                                type: 'solid',
                            },
                        },
                        axisLabel: {
                            color: '#DFEEF3',
                            fontSize: 12,
                            // 智能调整x轴标签显示间隔，避免拥挤
                            interval: name && name.length > 15 ? Math.ceil(name.length / 8) - 1 : 'auto',
                            rotate: name && name.length > 20 ? 45 : 0,
                            // formatter:function(value){
                            //     let arr=value.split(' ')
                            //     return arr[0]+'\n'+arr[1]
                            // }
                        },
                    },
                    yAxis: [
                        {
                            type: 'value',
                            name: '温度(℃)',
                            nameTextStyle: {
                                color: '#DFEEF3',
                                fontSize: 14,
                                padding: [0, 0, 0, 10], // [上, 右, 下, 左] 调整左边距来改变横向位置
                            },
                            axisLabel: {
                                color: '#DFEEF3',
                                fontSize: 12,
                                formatter: function(value) {
                                    return Math.round(value);
                                }
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: 'rgba(255,255,255,0.2)',
                                    type: 'solid',
                                },
                            },
                            splitLine: {
                                lineStyle: {
                                    color: 'rgba(255,255,255,0.2)',
                                    type: 'solid',
                                },
                            },
                        },
                        {
                            type: 'value',
                            name: '湿度(%RH)',
                            nameTextStyle: {
                                color: '#DFEEF3',
                                fontSize: 14,
                            },
                            axisLabel: {
                                color: '#DFEEF3',
                                fontSize: 12,
                                formatter: function(value) {
                                    return Math.round(value) + '%';
                                }
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: 'rgba(255,255,255,0.2)',
                                    type: 'solid',
                                },
                            },
                            splitLine: {
                                lineStyle: {
                                    color: 'rgba(255,255,255,0.2)',
                                    type: 'solid',
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            data: TAvg,
                            type: 'line',
                            yAxisIndex: 0,
                            name: '温度',
                            symbol: 'none', // 去除折线上的圆点
                            itemStyle: {
                                normal: {
                                    color: '#52F9BC',
                                },
                            },
                            lineStyle: {
                                width: 2,
                                color: '#52F9BC',
                            }
                        },
                        {
                            data: HAvg,
                            type: 'line',
                            yAxisIndex: 1,
                            name: '湿度',
                            symbol: 'none', // 去除折线上的圆点
                            itemStyle: {
                                normal: {
                                    color: '#34A7FF',
                                },
                            },
                            lineStyle: {
                                width: 2,
                                color: '#34A7FF',
                            }
                        },
                    ],
                },
            }
        })
    },

    // 二氧化碳浓度折线图数据
    CO2Line(list) {
        let name = [],
            data = []
        if (list && list.length > 0) {
            list.forEach(v => {
                name.push(v.time)
                data.push(v.value || 0)
            })
        } else {
            // 如果没有数据，使用空数组
            name = []
            data = []
        }
        return Promise.resolve().then(() => {
            // 如果没有数据，返回不显示图表
            if (name.length === 0) {
                return {
                    show: false,
                    option: {}
                }
            }
            
            return {
                show: true,
                option: {
                    legend: {
                        top: '8%',
                        left: 'center',
                        textStyle: {
                            color: '#DFEEF3',
                            fontSize: 12,
                        },
                        data: [
                            // {
                            //     name: '二氧化碳浓度',
                            //     icon: 'rect',
                            //     itemStyle: {
                            //         color: '#E9A31F'
                            //     }
                            // }
                        ],
                        itemWidth: 14,
                        itemHeight: 2,
                    },
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: 'rgba(0,0,0,.5)',
                        borderColor: 'transparent',
                        textStyle: {
                            color: '#DFEEF3',
                            fontSize: 14,
                        },
                        formatter: function(params) {
                            let result = '<div style="text-align: center; font-size: 14px; color: #DFEEF3;">' + params[0].name + '</div>'
                            params.forEach(function(item) {
                                let color = '#34A7FF'
                                result += '<div style="text-align: center; font-size: 14px; color:' + color + '">' +
                                         item.value + ' ppm</div>'
                            })
                            return result
                        }
                    },
                    grid: {
                        left: '2%',
                        right: '4%',
                        top: '25%',
                        bottom: '15%',
                        containLabel: true,
                    },
                    xAxis: {
                        type: 'category',
                        data: name,
                        boundaryGap: false,
                        axisTick: {
                            show: false,
                            alignWithLabel: true,
                        },
                        axisLine: {
                            lineStyle: {
                                color: 'rgba(0,242,255,0.2)',
                                type: 'solid',
                            },
                        },
                        axisLabel: {
                            color: '#DFEEF3',
                            fontSize: 12,
                            // 智能调整x轴标签显示间隔，避免拥挤
                            interval: name && name.length > 15 ? Math.ceil(name.length / 8) - 1 : 'auto',
                            rotate: name && name.length > 20 ? 45 : 0,
                        },
                    },
                    yAxis: {
                        type: 'value',
                        name: '二氧化碳(ppm)',
                        nameLocation: 'end',
                        nameGap: 28,
                        // 动态自适应y轴范围
                        min: function(value) {
                            return Math.max(0, value.min - (value.max - value.min) * 0.1);
                        },
                        max: function(value) {
                            return value.max + (value.max - value.min) * 0.1;
                        },
                        nameTextStyle: {
                            color: '#DFEEF3',
                            fontSize: 14,
                            padding: [0, 0, 0, 65], // [上, 右, 下, 左] 调整左边距来改变横向位置
                        },
                        axisLabel: {
                            color: '#DFEEF3',
                            fontSize: 12,
                            formatter: function(value) {
                                return Math.round(value);
                            }
                        },
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: 'rgba(255,255,255,0.2)',
                                type: 'solid',
                            },
                        },
                        splitLine: {
                            lineStyle: {
                                color: 'rgba(255,255,255,0.2)',
                                type: 'dashed',
                            },
                        },
                    },
                    series: [{
                        name: '二氧化碳浓度',
                        type: 'line',
                        data: data,
                        symbol: 'none', // 去除折线上的圆点
                        lineStyle: {
                            color: '#34A7FF',
                        },
                        itemStyle: {
                            color: '#34A7FF',
                        },
                        smooth: false,
                    }]
                }
            }
        })
    },

    // 气象->光照度折线图数据
    IlluminanceLine(list) {
        let name = [],
            data = []
        if (list && list.length > 0) {
            list.forEach(v => {
                name.push(v.showTime)
                data.push(v.illuminanceAvg || 0)
            })
        } else {
            // 如果没有数据，使用空数组
            name = []
            data = []
        }
        return Promise.resolve().then(() => {
            // 如果没有数据，返回不显示图表
            if (name.length === 0) {
                return {
                    show: false,
                    option: {}
                }
            }
            
            return {
                show: true,
                option: {
                    legend: {
                        top: '8%',
                        left: 'center',
                        textStyle: {
                            color: '#DFEEF3',
                            fontSize: 12,
                        },
                        data: [
                            // {
                            //     name: '光照强度',
                            //     icon: 'rect',
                            //     itemStyle: {
                            //         color: '#52F9BC'
                            //     }
                            // }
                        ],
                        itemWidth: 14,
                        itemHeight: 2,
                    },
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: 'rgba(0,0,0,.5)',
                        borderColor: 'transparent',
                        textStyle: {
                            color: '#DFEEF3',
                            fontSize: 14,
                        },
                        formatter: function(params) {
                            let result = '<div style="text-align: center; font-size: 14px; color: #DFEEF3;">' + params[0].name + '</div>'
                            params.forEach(function(item) {
                                let color = '#52F9BC'
                                result += '<div style="text-align: center; font-size: 14px; color:' + color + '">' +
                                         item.value + ' lux</div>'
                            })
                            return result
                        }
                    },
                    grid: {
                        left: '2%',
                        right: '4%',
                        top: '25%',
                        bottom: '15%',
                        containLabel: true,
                    },
                    xAxis: {
                        type: 'category',
                        data: name,
                        boundaryGap: false,
                        axisTick: {
                            show: false,
                            alignWithLabel: true,
                        },
                        axisLine: {
                            lineStyle: {
                                color: 'rgba(0,242,255,0.2)',
                                type: 'solid',
                            },
                        },
                        axisLabel: {
                            color: '#DFEEF3',
                            fontSize: 12,
                            // 智能调整x轴标签显示间隔，避免拥挤
                            interval: name && name.length > 15 ? Math.ceil(name.length / 8) - 1 : 'auto',
                            rotate: name && name.length > 20 ? 45 : 0,
                        },
                    },
                    yAxis: {
                        type: 'value',
                        name: '光照度(lux)',
                        nameLocation: 'end',
                        nameGap: 30,
                        // 动态自适应y轴范围
                        min: function(value) {
                            return Math.max(0, value.min - (value.max - value.min) * 0.1);
                        },
                        max: function(value) {
                            return value.max + (value.max - value.min) * 0.1;
                        },
                        nameTextStyle: {
                            color: '#DFEEF3',
                            fontSize: 14,
                            padding: [0, 0, 0, 40], // [上, 右, 下, 左] 调整左边距来改变横向位置
                        },
                        axisLabel: {
                            color: '#DFEEF3',
                            fontSize: 12,
                            formatter: function(value) {
                                return Math.round(value);
                            }
                        },
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: 'rgba(255,255,255,0.2)',
                                type: 'solid',
                            },
                        },
                        splitLine: {
                            lineStyle: {
                                color: 'rgba(255,255,255,0.2)',
                                type: 'dashed',
                            },
                        },
                    },
                    series: [{
                        name: '光照强度',
                        type: 'line',
                        data: data,
                        symbol: 'none', // 去除折线上的圆点
                        lineStyle: {
                            color: '#52F9BC',
                        },
                        itemStyle: {
                            color: '#52F9BC',
                        },
                        smooth: false,
                    }]
                }
            }
        })
    },

    // 气象->风向柱状图数据
    windBar(info) {
        return Promise.resolve().then(() => {
            let list = ['0-3', '3-6', '6-9', '9-11', '11-14', '14-17']
            let series = []
            let data0=[],data1=[],data2=[],data3=[],data4=[],data5=[]
            info.forEach(v => {
                data0.push(v.proportion[0])
                data1.push(v.proportion[1])
                data2.push(v.proportion[2])
                data3.push(v.proportion[3])
                data4.push(v.proportion[4])
                data5.push(v.proportion[5])
            })
            for(let i=0;i<6;i++){
                let data
                switch (i) {  
                    case 0:
                        data=data0
                        break
                    case 1:
                        data=data1
                        break
                    case 2:
                        data=data2
                        break
                    case 3:
                        data=data3
                        break
                    case 4:
                        data=data4
                        break
                    case 5:
                        data=data5
                        break
                }
                series.push({
                    type: 'bar',
                    data: data,
                    coordinateSystem: 'polar',
                    name: list[i],
                    stack: 'a',
                    emphasis: {
                        focus: 'series',
                    },
                })
            }
            return {
                show: true,
                option: {
                    color: ['#2326AF', '#6251B5', '#6666FF', '#4DB797', '#97D6E8', '#FEE378'],
                    // , '#FD9D6F', '#F37021'
                    grid: {
                        left: '5%',
                        right: '20%',
                        top: '5%',
                        bottom: '5%',
                        containLabel: true,
                    },
                    legend: {
                        orient: 'vertical',
                        bottom: '5%',
                        right: '3%',
                        itemGap: 20,
                        itemWidth: 14,
                        textStyle: {
                            color: '#DFEEF3',
                            fontSize: 14,
                        },
                    },
                    angleAxis: {
                        type: 'category',
                        data: ['北', '东北', '东', '东南', '南', '西南', '西', '西北'],
                        boundaryGap: false,
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: '#00F4FD',
                            },
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#00F4FD',
                                type: 'dashed',
                            },
                        },
                        axisLabel: {
                            color: '#DFEEF3',
                            fontSize: 12,
                        },
                    },
                    radiusAxis: {
                        type: 'value',
                        axisTick: {
                            show: false,
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#00F4FD',
                                type: 'dashed',
                            },
                        },
                    },
                    polar: {},
                    series: series,
                },
            }
        })
    },
    //累计灌溉柱状图信息
    cumulativeIrrigationBar(areaId) {
        return IrrigationService.cumulativeIrrigationListInformation(areaId).then(res => {
            var dataX = [],
                dataY = []
            dataX = res.map(item => {
                return item.irrigationDate
            })
            dataY = res.map(item => {
                return item.irrigateSum
            })
            return {
                show: true,
                option: {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: 'cross', // 默认为直线，可选为：'line' | 'shadow'
                            lineStyle: {
                                color: '#FFCD35',
                            },
                            label: {
                                show: false,
                            },
                        },
                        showContent: false,
                    },
                    grid: {
                        left: '5%',
                        right: '5%',
                        bottom: '7%',
                        top: '18%',
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: dataX,
                            axisTick: {
                                show: false,
                                alignWithLabel: true,
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: '#669DFF',
                                },
                            },
                            axisLabel: {
                                color: '#DFEEF3',
                                interval: 0,
                            },
                        },
                    ],
                    yAxis: [
                        {
                            name: '累计/m³',
                            nameTextStyle: {
                                color: '#DFEEF3',
                            },
                            // type : 'category',
                            // data : ['10','20','30','40'],
                            axisLabel: {
                                color: '#DFEEF3',
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: '#669DFF',
                                },
                            },
                            splitLine: {
                                lineStyle: {
                                    type: 'dashed',
                                    // color:'#669DFF'
                                    color: 'rgba(25,225,203,0.2)',
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: '',
                            type: 'bar',
                            barWidth: '14',
                            data: dataY,
                            // data:that.monthList,
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                                        { offset: 0, color: 'rgba(25,225,203, 0)' },
                                        { offset: 1, color: '#19E1CB' },
                                    ]),
                                    barBorderRadius: [10, 10, 0, 0],
                                },
                            },
                        },
                    ],
                    label: {
                        normal: {
                            show: true,
                            position: 'top',
                            formatter: '{c}',
                            color: '#fff',
                        },
                    },
                    emphasis: {
                        label: {
                            show: true,
                            position: [-10, -20],
                            color: '#FFCD35',
                        },
                        itemStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [
                                    {
                                        offset: 0,
                                        color: '#F6A334', // 0% 处的颜色
                                    },
                                    {
                                        offset: 1,
                                        color: 'rgba(246,163,52,0)', // 100% 处的颜色
                                        // color:'#F390AA'
                                    },
                                ],
                                global: false, // 缺省为 false
                            },
                        },
                    },
                },
            }
        })
    },
    //土壤墒情折线图数据
    flowVelocityLine(areaId) {
        return IrrigationService.cumulativeSoilMoistureListInformation(areaId).then(res => {
            var dataX = [],
                dataY = []
            dataX = res.map(item => {
                return item.soilMoistureDate
            })
            dataY = res.map(item => {
                return item.soilMoistureSum
            })
            return {
                show: true,
                option: {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            lineStyle: {
                                color: 'transparent',
                                type: 'dashed',
                            },
                        },
                        backgroundColor: 'rgba(1,13,23,0.8)',
                        borderColor: 'transparent',
                        textStyle: {
                            color: '#DFEEF3',
                        },
                        formatter: function(params) {
                            if (!params[0].name) {
                                return ''
                            } else {
                                var tipHtml =
                                    '<div style="text-align: center;">' +
                                    '<div style="font-size: 14px; color: #00F4DA;">' +
                                    params[0].name +
                                    '</div>' +
                                    '<div style="font-size: 14px; color: #00F4DA;">墒情：' +
                                    params[0].value +
                                    '%</div>' +
                                    '</div>'
                                return tipHtml
                            }
                        },
                    },
                    grid: {
                        left: '5%',
                        right: '5%',
                        bottom: '7%',
                        top: '18%',
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: dataX,
                            // data:name,
                            axisTick: {
                                show: false,
                                alignWithLabel: true,
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: '#669DFF',
                                },
                            },
                            axisLabel: {
                                color: '#DFEEF3',
                                interval: 0,
                            },
                        },
                    ],
                    yAxis: [
                        {
                            name: '墒情%',
                            nameTextStyle: {
                                color: '#DFEEF3',
                            },
                            // type : 'category',
                            // data : ['10','20','30','40'],
                            axisLabel: {
                                color: '#DFEEF3',
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: '#669DFF',
                                },
                            },
                            splitLine: {
                                lineStyle: {
                                    type: 'dashed',
                                    // color:'#669DFF'
                                    color: 'rgba(25,225,203,0.2)',
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: '',
                            type: 'line',
                            barWidth: '14',
                            data: dataY,
                            smooth: true,
                            // data:that.monthList,
                            areaStyle: {
                                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                                    { offset: 0, color: 'rgba(25,225,203, 0)' },
                                    { offset: 1, color: '#19E1CB' },
                                ]),
                            },
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                                        { offset: 0, color: 'rgba(25,225,203, 0)' },
                                        { offset: 1, color: '#19E1CB' },
                                    ]),
                                    barBorderRadius: [10, 10, 0, 0],
                                },
                            },
                        },
                    ],
                    label: {
                        normal: {
                            show: true,
                            position: 'top',
                            formatter: '{c}',
                            color: '#fff',
                        },
                    },
                    emphasis: {
                        label: {
                            show: true,
                            position: [-10, -20],
                            color: '#FFCD35',
                        },
                        itemStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [
                                    {
                                        offset: 0,
                                        color: '#F6A334', // 0% 处的颜色
                                    },
                                    {
                                        offset: 1,
                                        color: 'rgba(246,163,52,0)', // 100% 处的颜色
                                        // color:'#F390AA'
                                    },
                                ],
                                global: false, // 缺省为 false
                            },
                        },
                    },
                },
            }
        })
    },
    // 土壤->土壤温湿度折线图数据
    soilTHLine(name, soilT, soilH) {
        return Promise.resolve().then(() => {
            // 如果没有数据，返回不显示图表
            if (!name || name.length === 0) {
                return {
                    show: false,
                    option: {}
                }
            }
            
            return {
                show: true,
                option: {
                    // title: {
                    //     text: '土壤温湿度变化曲线',
                    //     left: 'left',
                    //     textStyle: {
                    //         color: '#DFEEF3',
                    //         fontSize: 16,
                    //     },
                    // },
                    legend: {
                        top: '8%',
                        left: 'center',
                        textStyle: {
                            color: '#DFEEF3',
                            fontSize: 14,
                        },
                        data: [
                            {
                                name: '温度',
                                icon: 'rect',
                                itemStyle: {
                                    color: '#52F9BC'
                                }
                            },
                            {
                                name: '湿度',
                                icon: 'rect',
                                itemStyle: {
                                    color: '#00F4FD'
                                }
                            }
                        ],
                        itemWidth: 14,
                        itemHeight: 2,
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross',
                            lineStyle: {
                                color: '#FFFFFF',
                                type: 'dashed',
                            },
                            label: {
                                show: false
                            }
                        },
                        backgroundColor: 'rgba(0,0,0,.5)',
                        borderColor: 'transparent',
                        textStyle: {
                            color: '#B2CFFF',
                            fontSize: 14,
                        },
                        extraCssText: 'z-index:2',
                        formatter: function(params) {
                            let result = '<div style="text-align: center;">'
                            result += '<div style="font-size: 14px; color: #DFEEF3; margin-bottom: 4px;">' + params[0].name + '</div>'
                            result += '<div style="display: inline-block; text-align: left;">'
                            params.forEach(function(item) {
                                let unit = item.seriesName === '温度' ? '℃' : '%RH'
                                let color = item.seriesName === '温度' ? '#52F9BC' : '#00F4FD'
                                result += '<div style="font-size: 14px; color: #DFEEF3; white-space: nowrap;">' + item.seriesName + ': ' +
                                         '<span style="color:' + color + '">' + item.value + unit + '</span></div>'
                            })
                            result += '</div></div>'
                            return result
                        }
                    },
                    grid: {
                        left: '2%',
                        right: '5%',
                        top: '17%',
                        bottom: '15%',
                        containLabel: true,
                    },
                    xAxis: {
                        type: 'category',
                        data: name,
                        // data: ['5-12', '5-13', '5-14', '5-15', '5-16', '5-17', '5-18'],
                        boundaryGap: true,
                        axisTick: {
                            show: false,
                            alignWithLabel: true,
                        },
                        axisLine: {
                            lineStyle: {
                                color: 'rgba(0,242,255,1)',
                                type: 'solid',
                            },
                        },
                        axisLabel: {
                            color: '#FFFFFF',
                            fontSize: 12,
                            // 智能调整x轴标签显示间隔，避免拥挤
                            interval: name && name.length > 15 ? Math.ceil(name.length / 8) - 1 : 'auto',
                            rotate: name && name.length > 20 ? 45 : 0,
                        },
                    },
                    yAxis: [
                        {
                            type: 'value',
                            name: '温度(℃)',
                             nameLocation: 'end',
                            nameGap: 25,
                            // 动态自适应y轴范围
                            min: function(value) {
                                return Math.max(0, value.min - (value.max - value.min) * 0.1);
                            },
                            max: function(value) {
                                return value.max + (value.max - value.min) * 0.1;
                            },
                            nameTextStyle: {
                                color: '#FFFFFF',
                                fontSize: 14,
                                padding: [0, 0, 0, 10], // [上, 右, 下, 左] 调整左边距来改变横向位置
                            },
                            axisLabel: {
                                color: '#FFFFFF',
                                fontSize: 12,
                                formatter: function(value) {
                                    return Math.round(value);
                                }
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: 'rgba(255,255,255,0.2)',
                                    type: 'solid',
                                },
                            },
                            splitLine: {
                                lineStyle: {
                                    color: 'rgba(255,255,255,0.2)',
                                    type: 'dashed',
                                },
                            },
                        },
                        {
                            type: 'value',
                            name: '湿度(%RH)',
                            nameLocation: 'end',
                            nameGap: 25,
                            // 动态自适应y轴范围
                            min: function(value) {
                                return Math.max(0, value.min - (value.max - value.min) * 0.1);
                            },
                            max: function(value) {
                                return Math.min(100, value.max + (value.max - value.min) * 0.1);
                            },
                            
                            nameTextStyle: {
                                color: '#FFFFFF',
                                fontSize: 14,
                            },
                            axisLabel: {
                                color: '#FFFFFF',
                                fontSize: 12,
                                formatter: function(value) {
                                    return Math.round(value) + '%';
                                }
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: 'rgba(255,255,255,0.2)',
                                    type: 'solid',
                                },
                            },
                            splitLine: {
                                show: false,
                            },
                        },
                    ],
                    series: [
                        {
                            // data: [12.3, 25, 63, 36.5, 22, 6, 45],
                            data: soilT,
                            type: 'line',
                            smooth: false,
                            yAxisIndex: 0,
                            symbol:'none',
                            name: '温度',
                            itemStyle: {
                                normal: {
                                    color: '#52F9BC',
                                },
                            },
                            lineStyle: {
                                color: '#52F9BC',
                                width: 2,
                            }
                        },
                        {
                            // data: [63, 33, 12, 5, 95, 12.3, 45],
                            data: soilH,
                            type: 'line',
                            smooth: false,
                            yAxisIndex: 1,
                            symbol: 'none', // 去除折线上的圆点
                            name: '湿度',
                            itemStyle: {
                                normal: {
                                    color: '#00F4FD',
                                },
                            },
                            lineStyle: {
                                color: '#00F4FD',
                                width: 2,
                            }
                        },
                    ],
                },
            }
        })
    },
    // 土壤->PH值折线图数据
    phLine(name, ph) {
        return Promise.resolve().then(() => {
            return {
                show: true,
                option: {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            lineStyle: {
                                color: 'transparent',
                                type: 'dashed',
                            },
                        },
                        backgroundColor: 'rgba(0,0,0,.5)',
                        borderColor: 'transparent',
                        textStyle: {
                            color: '#B2CFFF',
                            fontSize: 14,
                        },
                        extraCssText: 'z-index:2',
                        formatter: function(params) {
                            let result = '<div style="text-align: center; font-size: 14px;">' + params[0].name + '</div>'
                            params.forEach(function(item) {
                                result += '<div style="text-align: center; font-size: 14px; color:' + item.color + '">' +
                                         'PH值: ' + item.value + '</div>'
                            })
                            return result
                        }
                    },
                    grid: {
                        left: '8%',
                        right: '4%',
                        top: '15%',
                        bottom: '15%',
                        containLabel: false,
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: true,
                        // data: ['1.11', '1.12', '1.13', '1.14', '1.15', '1.16', '1.17'],
                        data: name,
                        axisTick: {
                            show: false,
                            alignWithLabel: true,
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#00F2FF',
                            },
                        },
                        axisLabel: {
                            color: '#DFEEF3',
                            interval: 0,
                        },
                    },
                    yAxis: {
                        name: 'PH值',
                        nameTextStyle: {
                            color: '#DFEEF3',
                            fontSize: 14,
                        },
                        type: 'value',
                        axisLabel: {
                            show: true,
                            color: '#DFEEF3',
                        },
                        axisTick: {
                            show: false,
                        },
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: '#669EFF',
                            },
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: 'rgba(0,242,255,.4)',
                                type: 'solid',
                            },
                        },
                    },
                    series: [
                        {
                            // data: [93, 932, 901, 15, 820, 1330, 150],
                            data: ph,
                            type: 'line',
                            itemStyle: {
                                color: '#4CCAEE',
                            },
                        },
                    ],
                },
            }
        })
    },
    // 土壤->电导率折线图数据
    conductivityLine(name, conductivity) {
        return Promise.resolve().then(() => {
            // 如果没有数据，返回不显示图表
            if (!name || name.length === 0) {
                return {
                    show: false,
                    option: {}
                }
            }
            
            return {
                show: true,
                option: {
                    // title: {
                    //     text: '电导率变化曲线',
                    //     left: 'left',
                    //     textStyle: {
                    //         color: '#DFEEF3',
                    //         fontSize: 16,
                    //     },
                    // },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross',
                            lineStyle: {
                                color: '#FFFFFF',
                                type: 'dashed',
                            },
                            label: {
                                show: false
                            }
                        },
                        backgroundColor: 'rgba(0,0,0,.5)',
                        borderColor: 'transparent',
                        textStyle: {
                            color: '#4CCAEE',
                            fontSize: 14,
                        },
                        extraCssText: 'z-index:2',
                        formatter: function(params) {
                            let result = '<div style="text-align: center; font-size: 14px; color: #DFEEF3;">' + params[0].name + '</div>'
                            params.forEach(function(item) {
                                let color = '#4CCAEE'
                                result += '<div style="text-align: center; font-size: 14px; color:' + color + '">' +
                                         item.value + ' μS/cm</div>'
                            })
                            return result
                        }
                    },
                    grid: {
                        left: '2.5%',
                        right: '5%',
                        top: '17%',
                        bottom: '15%',
                        containLabel: true,
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: true,
                        // data: ['1.11', '1.12', '1.13', '1.14', '1.15', '1.16', '1.17'],
                        data: name,
                        axisTick: {
                            show: false,
                            alignWithLabel: true,
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#00F2FF',
                            },
                        },
                        axisLabel: {
                            color: '#DFEEF3',
                            // 智能调整x轴标签显示间隔，避免拥挤
                            interval: name && name.length > 15 ? Math.ceil(name.length / 8) - 1 : 'auto',
                            rotate: name && name.length > 20 ? 45 : 0,
                        },
                    },
                    yAxis: {
                        name: '电导率(μS/cm)',
                        // 动态自适应y轴范围
                        min: function(value) {
                            return Math.max(0, value.min - (value.max - value.min) * 0.1);
                        },
                        max: function(value) {
                            return value.max + (value.max - value.min) * 0.1;
                        },
                        nameGap:30,
                        nameTextStyle: {
                            color: '#DFEEF3',
                            fontSize: 14,
                            padding: [0, 0, 0, 30]
                        },
                        type: 'value',
                        axisLabel: {
                            show: true,
                            color: '#DFEEF3',
                            formatter: function(value) {
                                return Math.round(value);
                            }
                        },
                        axisTick: {
                            show: false,
                        },
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: '#669EFF',
                            },
                        },
                        splitLine: {
                            lineStyle: {
                                color: 'rgba(255,255,255,0.2)',
                                type: 'dashed',
                            },
                        },
                    },
                    series: [
                        {
                            name: '电导率',
                            data: conductivity,
                            type: 'line',
                            symbol:'none',
                            itemStyle: {
                                color: '#4CCAEE',
                            },
                            lineStyle: {
                                color: '#4CCAEE',
                                width: 2,
                            }
                        },
                    ],
                },
            }
        })
    },
    // 土壤->氮磷钾折线图数据
    NpkLine(name, N, P, K) {
        return Promise.resolve().then(() => {
            return {
                show: true,
                option: {
                    legend: {
                        top: '0%',
                        // right:"25%",
                        textStyle: {
                            color: '#B8D4FF',
                        },
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            lineStyle: {
                                color: 'transparent',
                                type: 'dashed',
                            },
                        },
                        backgroundColor: 'rgba(0,0,0,.5)',
                        borderColor: 'transparent',
                        textStyle: {
                            color: '#DFEEF3',
                            fontSize: 14,
                        },
                        extraCssText: 'z-index:2',
                        formatter: function(params) {
                            let result = '<div style="text-align: center; font-size: 14px;">' + params[0].name + '</div>'
                            params.forEach(function(item) {
                                result += '<div style="text-align: center; font-size: 14px; color:' + item.color + '">' +
                                         item.seriesName + ': ' + item.value + ' mg/kg</div>'
                            })
                            return result
                        }
                    },
                    grid: {
                        left: '10%',
                        right: '5%',
                        top: '18%',
                        bottom: '13%',
                        containLabel: false,
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: true,
                        // data: ['1.11', '1.12', '1.13', '1.14', '1.15', '1.16', '1.17'],
                        data: name,
                        axisTick: {
                            show: false,
                            alignWithLabel: true,
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#00F2FF',
                            },
                        },
                        axisLabel: {
                            color: '#DFEEF3',
                            interval: 0,
                        },
                    },
                    yAxis: {
                        name: '氮磷钾浓度(mg/kg)',
                        nameTextStyle: {
                            color: '#DFEEF3',
                            fontSize: 14,
                        },
                        type: 'value',
                        axisLabel: {
                            show: true,
                            color: '#DFEEF3',
                        },
                        axisTick: {
                            show: false,
                        },
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: '#669EFF',
                            },
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: 'rgba(0,242,255,.4)',
                                type: 'solid',
                            },
                        },
                    },
                    series: [
                        {
                            // data: [93, 932, 901, 15, 820, 1330, 150],
                            data: N,
                            type: 'line',
                            name: '氮',
                            itemStyle: {
                                color: '#FFDC4F',
                            },
                        },
                        {
                            // data: [48, 369, 102, 444, 22, 52, 13],
                            data: P,
                            type: 'line',
                            name: '磷',
                            itemStyle: {
                                color: '#4CCAEE',
                            },
                        },
                        {
                            // data: [455, 932, 36, 246, 138, 952, 555],
                            data: K,
                            type: 'line',
                            name: '钾',
                            itemStyle: {
                                color: '#0BFFA0',
                            },
                        },
                    ],
                },
            }
        })
    },
    //虫情-> 虫情监测总量图表
    InsectSituationOverviewBar(info) {
        let nameData = [] // x轴时刻列表
        let allLabel = [] //所有的虫子名称
        let series = [] // echart需要的序列数据
        let datalegend = [] //legend数据
        // let gridTop = 0 //图表距离上方距离

        let getValueArrayByName = name => {
            let values = []
            info.forEach(infoElement => {
                if (infoElement.wormCaseDetailList) {
                    let voArray = infoElement.wormCaseDetailList.filter(vo => {
                        return vo.count == name
                    })
                    if (voArray && voArray.length) {
                        let value = 0
                        voArray.forEach(v => (value += v.count))
                        values.push(value)
                        return
                    }
                }
                values.push(0)
            })
            return values
        }
        info.forEach(infoElement => {
            nameData.push(infoElement.collectTime)
            infoElement.wormCaseDetailList &&
                infoElement.wormCaseDetailList.forEach(vo => {
                    if (!allLabel.includes(vo.kind)) {
                        allLabel.push(vo.kind)
                        series.push({
                            name: 虫情种类._lableOf(vo.kind),
                            type: 'bar',
                            barWidth: '25',
                            stack: 'total',
                            data: getValueArrayByName(vo.count),
                        })
                    }
                })
        })
        datalegend = chart.adjustLegendData(allLabel)
        // gridTop = chart.getGridTop(allLabel)
        // console.log(series);
        return Promise.resolve().then(() => {
            return {
                show: true,
                option: {
                    color: ['#04B0BA', '#B8D4FF', '#4CCAEE', '#FFC107', '#90ED7D', '#3F51B5'],
                    tooltip: {
                        trigger: 'axis',
                        extraCssText: 'z-index:2',
                        axisPointer: {
                            lineStyle: {
                                color: 'transparent',
                                type: 'dashed',
                            },
                        },
                        backgroundColor: 'rgba(0,0,0,.5)',
                        borderColor: 'transparent',
                        textStyle: {
                            color: '#DFEEF3',
                        },
                    },
                    legend:{
                        itemHeight: 10,
                        itemWidth: 10,
                        textStyle: {
                            color: '#DFEEF3',
                            fontSize: 14,
                        },
                    },
                    grid: {
                        left: '3%',
                        right: '3%',
                        top: '20%',
                        // top: gridTop,
                        bottom: '7%',
                        containLabel: true,
                    },
                    xAxis: {
                        type: 'category',
                        data: nameData,
                        axisTick: {
                            show: false,
                            alignWithLabel: true,
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#00F2FF',
                            },
                        },
                        axisLabel: {
                            color: '#DFEEF3',
                            fontSize: 12,
                        },
                    },
                    yAxis: {
                        type: 'value',
                        name: '虫情种数',
                        nameTextStyle: {
                            color: '#DFEEF3',
                            fontSize: 14,
                        },
                        axisLabel: {
                            color: '#DFEEF3',
                            fontSize: 12,
                        },
                        splitLine: {
                            lineStyle: {
                                type: 'solid',
                                color: 'rgba(0,242,255,.4)',
                            },
                        },
                    },
                    series,
                },
            }
        })
    },
    // 处理legend数据
    adjustLegendData(legendData) {
        let processLegend = []
        // 调整图例显示样式 {图例太多时，Echarts文档注明: 特殊字符串 ''（空字符串）或者 '\n' (换行字符串)用于图例的换行。}
        for (let i = 0, j = 0; i < legendData.length; i++, j++) {
            // 设置一行显示6个图标
            if (i % LINE_NUM_EACH_ROW == 0) {
                processLegend.push('')
            }
            processLegend.push(legendData[i])
        }
        return processLegend
    },
    // 处理图表距离上方距离
    getGridTop(legendData) {
        var DEFAULT_LINE_NUM = 6 // 采用默认grid.top值的默认线条数目；
        var DEFAULT_GRID_TOP_PECENTAGE = 18 // 默认的grid.top百分比（整数部分）；
        var DELTA_GRID_TOP_PECENTAGE = 5 // 超出默认线条数时的grid.top百分比增量（整数部分）；
        var MAX_GRID_TOP_PECENTAGE = 20 // 最大的grid.top百分比（整数部分）；

        var topInt
        var gridTop
        var len = legendData.length

        // 如果图例太多，需要调整option中的grid.top值才能避免重叠
        topInt =
            len > DEFAULT_LINE_NUM
                ? DEFAULT_GRID_TOP_PECENTAGE +
                  DELTA_GRID_TOP_PECENTAGE * Math.ceil((len - DEFAULT_LINE_NUM) / LINE_NUM_EACH_ROW)
                : DEFAULT_GRID_TOP_PECENTAGE
        if (topInt >= MAX_GRID_TOP_PECENTAGE) {
            topInt = 30
        }

        gridTop = topInt + '%'
        return gridTop
    },
    // 虫情->虫情变化趋势
    WormCaseVariation(info) {
        let nameData = [] // x轴时刻列表
        let allLabel = [] //所有的虫子名称
        let series = [] // echart需要的序列数据
        let getValueArrayByName = name => {
            let values = []
            info.forEach(infoElement => {
                if (infoElement.wormCaseDensityAndTimeList) {
                    let voArray = infoElement.wormCaseDensityAndTimeList.filter(vo => {
                        return vo.density == name
                    })
                    if (voArray && voArray.length) {
                        let value = 0
                        voArray.forEach(v => (value += v.density))
                        values.push(value)
                        return
                    }
                }
                values.push(0)
            })
            return values
        }
        info.forEach(infoElement => {
            nameData.push(infoElement.collectTime)
            infoElement.wormCaseDensityAndTimeList &&
                infoElement.wormCaseDensityAndTimeList.forEach(vo => {
                    if (!allLabel.includes(vo.kind)) {
                        allLabel.push(vo.kind)
                        series.push({
                            name: 虫情种类._lableOf(vo.kind),
                            type: 'line',
                            data: getValueArrayByName(vo.density),
                        })
                    }
                })
        })
        return Promise.resolve().then(() => {
            return {
                show: true,
                option: {
                    color: ['#04B0BA', '#B8D4FF', '#4CCAEE', '#FFC107', '#90ED7D', '#3F51B5'],
                    legend: {
                        icon: 'rect',
                        itemHeight: 10,
                        itemWidth: 10,
                        data: ['螟蛾', '稻飞虱', '金龟子', '蝗虫', '蚜虫', '红蜘蛛'],
                        top: '0%',
                        // right:"25%",
                        textStyle: {
                            color: '#B8D4FF',
                        },
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            lineStyle: {
                                color: 'transparent',
                                type: 'dashed',
                            },
                        },
                        backgroundColor: 'rgba(0,0,0,.5)',
                        borderColor: 'transparent',
                        textStyle: {
                            color: '#DFEEF3',
                        },
                        extraCssText: 'z-index:2',
                    },
                    grid: {
                        left: '10%',
                        right: '5%',
                        top: '18%',
                        bottom: '13%',
                        containLabel: false,
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: true,
                        // data: ['1.11', '1.12', '1.13', '1.14', '1.15', '1.16', '1.17'],
                        data: nameData,
                        axisTick: {
                            show: false,
                            alignWithLabel: true,
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#00F2FF',
                            },
                        },
                        axisLabel: {
                            color: '#DFEEF3',
                            interval: 0,
                        },
                    },
                    yAxis: {
                        name: '只/亩',
                        nameTextStyle: {
                            color: '#DFEEF3',
                            fontSize: 14,
                        },
                        type: 'value',
                        axisLabel: {
                            show: true,
                            color: '#DFEEF3',
                        },
                        axisTick: {
                            show: false,
                        },
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: '#669EFF',
                            },
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: 'rgba(0,242,255,.4)',
                                type: 'solid',
                            },
                        },
                    },
                    series: series,
                    //  [
                    //     {
                    //         // data: [93, 932, 901, 15, 820, 1330, 150],
                    //         data:N,
                    //         type: 'line',
                    //         name: '氮',
                    //         itemStyle: {
                    //             color: '#FFDC4F',
                    //         },
                    //     },
                    //     {
                    //         // data: [48, 369, 102, 444, 22, 52, 13],
                    //         data:P,
                    //         type: 'line',
                    //         name: '磷',
                    //         itemStyle: {
                    //             color: '#4CCAEE',
                    //         },
                    //     },
                    //     {
                    //         // data: [455, 932, 36, 246, 138, 952, 555],
                    //         data:K,
                    //         type: 'line',
                    //         name: '钾',
                    //         itemStyle: {
                    //             color: '#0BFFA0',
                    //         },
                    //     },
                    // ],
                },
            }
        })
    },
    // 总虫情种类占比
    InsectSituationTypePie(list) {
        let count = 0,
            data = []
        count = list.count == undefined || list.count == null ? 0 : list.count
        list.wormCaseProportionVoList.forEach(v => {
            data.push({
                name: 虫情种类._lableOf(v.kind),
                value: v.proportion,
            })
        })

        return Promise.resolve().then(() => {
            return {
                show: true,
                option: {
                    grid: {
                        left: '3%',
                        right: '3%',
                        top: '15%',
                        bottom: '20%',
                        containLabel: true,
                    },
                    graphic: {
                        //图形中间文字
                        type: 'text',
                        left: 'center',
                        top: 'center',
                        style: {
                            text: '总数' + '\n' + count + '只',
                            textAlign: 'center',
                            fill: '#FFFFFF',
                            fontSize: 14,
                        },
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: '{d}' + '%',
                        extraCssText: 'z-index:2',
                    },
                    series: [
                        {
                            name: 'Access From',
                            type: 'pie',
                            radius: ['50%', '70%'],
                            avoidLabelOverlap: true,
                            label: {
                                position: 'outer',
                                alignTo: 'labelLine',
                                bleedMargin: 5,
                                color: '#FFFFFF',
                                fontWeight: 'bold',
                            },
                            data: data,
                            // data: [
                            //     { name: '螟蛾', value: 5 },
                            //     { name: '蜱虫', value: 5 },
                            //     { name: '射炮步甲', value: 3 },
                            //     { name: '衣蛾', value: 8 },
                            //     { name: '小灰蛾', value: 4 },
                            //     { name: '瓢虫', value: 4 },
                            //     { name: '蛾蠓', value: 1 },
                            //     { name: '硬壳虫', value: 1 },
                            // ],
                        },
                    ],
                },
            }
        })
    },
    // 遥感数字化->作物长势分析
    cropsGrow() {
        return RemoteSensingService.findRemoteCropHeight().then(res => {
            let datetime = []
            // 小麦
            let wheat = []
            // 玉米
            let maize = []
            // 大豆
            let soybean = []
            // 水稻
            let rice = []
            res.forEach(item => {
                var month = item.dateTime.substr(5, 2)
                let day = item.dateTime.substr(8, 2)
                // console.log(month)
                datetime.push(`${month}月${day}日`)
                item.dataList.forEach(res => {
                    if (res.type == 0) {
                        soybean.push(res.height)
                    } else {
                        soybean.push(0)
                    }
                    if (res.type == 1) {
                        wheat.push(res.height)
                    } else {
                        wheat.push(0)
                    }
                    if (res.type == 2) {
                        maize.push(res.height)
                    } else {
                        maize.push(0)
                    }
                    if (res.type == 3) {
                        rice.push(res.height)
                    } else {
                        rice.push(0)
                    }
                })
            })
            // console.log(datetime)
            return {
                show: true,
                option: {
                    legend: {
                        top: '3%',
                        // bottom:'50%',
                        left: 'center',
                        // right:"25%",
                        icon: 'rect',
                        itemHeight: 12,
                        itemWidth: 12,
                        textStyle: {
                            color: '#B8D4FF',
                        },
                    },
                    dataZoom: [
                        {
                            // type: 'slider',
                            show: true,
                            xAxisIndex: [0],
                            left: '9%',
                            bottom: '2%',
                            start: 0,
                            end: 50, //初始化滚动条
                        },
                    ],
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            lineStyle: {
                                color: 'transparent',
                                type: 'dashed',
                            },
                        },
                        backgroundColor: 'rgba(0,0,0,.5)',
                        borderColor: 'transparent',
                        textStyle: {
                            color: '#DFEEF3',
                        },
                        extraCssText: 'z-index:2',
                    },
                    grid: {
                        left: '13%',
                        right: '5%',
                        top: '18%',
                        bottom: '23%',
                        containLabel: false,
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: datetime,
                        axisTick: {
                            show: false,
                            alignWithLabel: true,
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#00F2FF',
                            },
                        },
                        axisLabel: {
                            color: '#DFEEF3',
                            interval: 0,
                        },
                    },
                    yAxis: {
                        name: 'cm',
                        nameTextStyle: {
                            color: '#DFEEF3',
                            fontSize: 14,
                        },
                        type: 'value',
                        axisLabel: {
                            show: true,
                            color: '#DFEEF3',
                        },
                        axisTick: {
                            show: false,
                        },
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: '#669EFF',
                            },
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: 'rgba(0,242,255,.4)',
                                type: 'solid',
                            },
                        },
                    },
                    series: [
                        {
                            data: wheat,
                            type: 'line',
                            name: '小麦',
                            itemStyle: {
                                color: '#4CCAEE',
                            },
                        },
                        {
                            data: maize,
                            type: 'line',
                            name: '玉米',
                            itemStyle: {
                                color: '#FFDC4F',
                            },
                        },

                        {
                            data: soybean,
                            type: 'line',
                            name: '大豆',
                            itemStyle: {
                                color: '#0BFFA0',
                            },
                        },
                        {
                            data: rice,
                            type: 'line',
                            name: '水稻',
                            itemStyle: {
                                color: '#FF4E9D',
                            },
                        },
                    ],
                },
            }
        })
    },
    getPie3D(pieData, internalDiameterRatio) {
        //internalDiameterRatio:透明的空心占比
        let that = this
        let series = []
        let sumValue = 0
        let startValue = 0
        let endValue = 0
        let legendData = []
        let legendBfb = []
        let k = 1 - internalDiameterRatio
        // pieData.sort((a, b) => {
        //     return (b.value - a.value);
        // });
        // 为每一个饼图数据，生成一个 series-surface 配置
        for (let i = 0; i < pieData.length; i++) {
            sumValue += pieData[i].value
            let seriesItem = {
                name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
                type: 'surface',
                parametric: true,
                wireframe: {
                    show: false,
                },
                pieData: pieData[i],
                pieStatus: {
                    selected: false,
                    hovered: false,
                    k: k,
                },
                center: ['10%', '50%'],
            }

            if (typeof pieData[i].itemStyle != 'undefined') {
                let itemStyle = {}
                typeof pieData[i].itemStyle.color != 'undefined' ? (itemStyle.color = pieData[i].itemStyle.color) : null
                typeof pieData[i].itemStyle.opacity != 'undefined'
                    ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
                    : null
                seriesItem.itemStyle = itemStyle
            }
            series.push(seriesItem)
        }

        // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
        // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
        legendData = []
        legendBfb = []
        for (let i = 0; i < series.length; i++) {
            endValue = startValue + series[i].pieData.value
            series[i].pieData.startRatio = startValue / sumValue
            series[i].pieData.endRatio = endValue / sumValue
            // series[i].parametricEquation = chart.getParametricEquation(series[i].pieData.startRatio, series[i].pieData.endRatio,
            //     false, false, k, series[i].pieData.value);
            series[i].parametricEquation = chart.getParametricEquation(
                series[i].pieData.startRatio,
                series[i].pieData.endRatio,
                false,
                false,
                k,
                50
            )
            startValue = endValue
            let bfb = that.fomatFloat(series[i].pieData.value / sumValue, 4)
            legendData.push({
                name: series[i].name,
                value: bfb,
            })
            legendBfb.push({
                name: series[i].name,
                value: bfb,
            })
        }

        let boxHeight = chart.getHeight3D(series, 26) //通过传参设定3d饼/环的高度，26代表26px
        // 准备待返回的配置项，把准备好的 legendData、series 传入。
        let option = {
            color: ['#4CEAFF ', '#FF5252', '#FFC73B', '#DD7E7E', '#5E68C8', '#C5CC5B'],
            // legend: {
            //     data: legendData,
            //     orient: 'vertical',
            //     bottom: 'center',
            //     top: 'center',
            //     right: 40,
            //     itemGap: 15,
            //     itemWidth: 14,
            //     textStyle: {
            //         color: '#B8D4FF',
            //     },
            //     show: true,
            //     // icon: "circle",
            //     formatter: function(param) {
            //         let item = pieData.filter(item => item.name == param)[0]
            //         return `${item.name}  ${item.value} 只`
            //     },
            // },
            labelLine: {
                show: true,
                lineStyle: {
                    color: '#7BC0CB',
                },
            },
            // label: {
            //     show: true,
            //     position: 'vertical',
            //     // rich: {
            //     //     b: {
            //     //         color: '#7BC0CB',
            //     //         fontSize: 12,
            //     //         lineHeight: 20,
            //     //     },
            //     //     c: {
            //     //         fontSize: 16,
            //     //     },
            //     // },
            //     // formatter: '{b|{b} \n}{c|{c}}{b|  只}',
            // },
            tooltip: {
                formatter: params => {
                    if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
                        let bfb = (
                            (option.series[params.seriesIndex].pieData.endRatio -
                                option.series[params.seriesIndex].pieData.startRatio) *
                            100
                        ).toFixed(2)
                        return (
                            `${params.seriesName}<br/>` +
                            `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};"></span>` +
                            `${bfb}%`
                        )
                    }
                },
            },

            xAxis3D: {
                min: -1,
                max: 1,
            },
            yAxis3D: {
                min: -1,
                max: 1,
            },
            zAxis3D: {
                min: -1,
                max: 1,
            },
            grid3D: {
                show: false,
                left: '2%',
                boxHeight: boxHeight, //圆环的高度
                // boxHeight:1,
                viewControl: {
                    //3d效果可以放大、旋转等，请自己去查看官方配置
                    alpha: 25, //角度
                    // distance: 300,//调整视角到主体的距离，类似调整zoom
                    distance: 170,
                    rotateSensitivity: 0, //设置为0无法旋转
                    zoomSensitivity: 0, //设置为0无法缩放
                    panSensitivity: 0, //设置为0无法平移
                    autoRotate: true, //自动旋转
                },
            },
            series: series,
        }
        return option
    },
    //获取3d丙图的最高扇区的高度
    getHeight3D(series, height) {
        series.sort((a, b) => {
            return b.pieData.value - a.pieData.value
        })
        return (height * 25) / series[0].pieData.value
    },
    // 生成扇形的曲面参数方程，用于 series-surface.parametricEquation
    getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {
        // 计算
        let midRatio = (startRatio + endRatio) / 2
        let startRadian = startRatio * Math.PI * 2
        let endRadian = endRatio * Math.PI * 2
        let midRadian = midRatio * Math.PI * 2
        // 如果只有一个扇形，则不实现选中效果。
        if (startRatio === 0 && endRatio === 1) {
            isSelected = false
        }
        // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
        k = typeof k !== 'undefined' ? k : 1 / 3
        // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
        let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0
        let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0
        // 计算高亮效果的放大比例（未高亮，则比例为 1）
        let hoverRate = isHovered ? 1.05 : 1
        // 返回曲面参数方程
        return {
            u: {
                min: -Math.PI,
                max: Math.PI * 3,
                step: Math.PI / 32,
            },
            v: {
                min: 0,
                max: Math.PI * 2,
                step: Math.PI / 20,
            },
            x: function(u, v) {
                if (u < startRadian) {
                    return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
                }
                if (u > endRadian) {
                    return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
                }
                return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate
            },
            y: function(u, v) {
                if (u < startRadian) {
                    return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
                }
                if (u > endRadian) {
                    return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
                }
                return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate
            },
            z: function(u, v) {
                if (u < -Math.PI * 0.5) {
                    return Math.sin(u)
                }
                if (u > Math.PI * 2.5) {
                    return Math.sin(u) * h * 0.1
                }
                return Math.sin(v) > 0 ? 1 * h * 0.1 : -1
            },
        }
    },
    fomatFloat(num, n) {
        var f = parseFloat(num)
        if (isNaN(f)) {
            return false
        }
        f = Math.round(num * Math.pow(10, n)) / Math.pow(10, n) // n 幂
        var s = f.toString()
        var rs = s.indexOf('.')
        //判定如果是整数，增加小数点再补0
        if (rs < 0) {
            rs = s.length
            s += '.'
        }
        while (s.length <= rs + n) {
            s += '0'
        }
        return s
    },
}
export { chart }
